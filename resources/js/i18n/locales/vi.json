{"nav": {"brand": "CreateYourRun", "generateActivity": "<PERSON><PERSON><PERSON>", "export": "<PERSON>ất File", "getTokens": "<PERSON><PERSON>", "profile": "Hồ Sơ", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>"}, "footer": {"copyright": "© CreateYourRun. All rights reserved."}, "home": {"title": "<PERSON><PERSON>o mừng đến với CreateYourRun", "subtitle": "Tạo hoạt động chạy bộ ảo và đưa chúng lên các nền tảng yêu thích của bạn", "description": "<PERSON><PERSON><PERSON> các tuyến đường chạy bộ và đạp xe chân thực với các biến đổi tốc độ tự nhiên vư<PERSON><PERSON> qua thuật toán phân tích của <PERSON>ra<PERSON>.", "getStarted": "<PERSON><PERSON><PERSON>", "newFeature": {"title": "Mới! Tạo Tất Cả Trong Một", "description": "Tạo tuyến đường và cấu hình hoạt động trong một bước - nhanh hơn và dễ dàng hơn!", "button": "<PERSON><PERSON><PERSON> Tất <PERSON>"}, "features": {"createRoutes": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>t kế tuyến đường chạy bộ tùy chỉnh trên bản đồ tương tác. Thêm điểm dừng, điều chỉnh đường đi và tạo tuyến đường hoàn hảo.", "button": "<PERSON><PERSON><PERSON>"}, "generateActivities": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>n tuyến đường của bạn thành hoạt động thực tế với tốc độ, nh<PERSON><PERSON> tim, nh<PERSON><PERSON> bước và các chỉ số khác có thể tùy chỉnh.", "button": "<PERSON><PERSON><PERSON>"}, "exportActivities": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>ất hoạt động đã tạo của bạn ở nhiều định dạng khác nhau (GPX, TCX) để tải lên Strava và các nền tảng thể dục khác.", "button": "<PERSON><PERSON><PERSON>"}}, "whyUse": {"title": "Tại Sao Sử Dụng CreateYourRun?", "reason1": "<PERSON><PERSON><PERSON> nghiệm tuyến đường chạy mới một cách ảo trước khi thử chúng ngoài đời thực", "reason2": "<PERSON><PERSON><PERSON> dữ liệu tập luyện nhất quán trong quá trình chuyển đổi thiết bị", "reason3": "<PERSON><PERSON> trì chuỗi hoạt động của bạn khi đi du lịch hoặc bị thương", "reason4": "So sánh các chỉ số hiệu suất trên các tuyến đường khác nhau", "reason5": "Chia sẻ ý tưởng tuyến đường với bạn bè và nhóm chạy bộ"}}, "auth": {"login": {"title": "<PERSON><PERSON><PERSON>", "email": "Đ<PERSON>a Chỉ Email", "password": "<PERSON><PERSON><PERSON>", "rememberMe": "Ghi Nhớ <PERSON>", "forgotPassword": "<PERSON>u<PERSON><PERSON>?", "submit": "<PERSON><PERSON><PERSON>", "noAccount": "Chưa có tài k<PERSON>n?", "signUp": "<PERSON><PERSON><PERSON> ký tại đây"}, "register": {"title": "<PERSON><PERSON><PERSON> CreateYourRun", "name": "Họ và Tên", "email": "Đ<PERSON>a Chỉ Email", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>", "termsAgreement": "<PERSON><PERSON>i đồng ý với", "termsOfService": "<PERSON><PERSON><PERSON><PERSON>", "and": "và", "privacyPolicy": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "submitting": "<PERSON><PERSON> tạo tà<PERSON>...", "hasAccount": "Đã có tài k<PERSON>n?", "signIn": "<PERSON><PERSON><PERSON>"}, "errors": {"invalidCredentials": "Email hoặc mật kh<PERSON>u không hợp lệ", "emailRequired": "<PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc", "nameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "registrationFailed": "<PERSON><PERSON><PERSON> ký thất bại. <PERSON><PERSON> lòng thử lại."}}, "activity": {"generator": {"title": "<PERSON><PERSON><PERSON>", "routeCreator": "<PERSON><PERSON><PERSON>", "searchLocation": "<PERSON><PERSON><PERSON> kiếm vị trí...", "search": "<PERSON><PERSON><PERSON>", "aligning": "<PERSON><PERSON> căn chỉnh...", "clear": "Xóa", "activitySummary": "<PERSON><PERSON><PERSON> T<PERSON>t Hoạt Động", "distance": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Thời Gian", "avgPace": "Tốc Độ TB", "avgHeartRate": "<PERSON><PERSON><PERSON><PERSON>", "continueToExport": "<PERSON><PERSON><PERSON><PERSON>", "configureMessage": "<PERSON><PERSON><PERSON> hình các thông số hoạt động và nhấp <PERSON>", "activityParameters": "Thông Số Hoạt Động", "activityName": "<PERSON><PERSON><PERSON>", "activityType": "Lo<PERSON>i <PERSON>", "run": "Chạy Bộ", "bike": "<PERSON><PERSON><PERSON>", "hike": "<PERSON><PERSON>", "activityDate": "<PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON><PERSON><PERSON> G<PERSON>", "pace": "Tốc Độ (phút/km)", "paceDisplay": "(<PERSON><PERSON><PERSON> là cách hiển thị trên <PERSON>)", "paceVariability": "<PERSON><PERSON><PERSON><PERSON> (%)", "avgHeartRateLabel": "<PERSON><PERSON><PERSON><PERSON> (bpm)", "cadence": "<PERSON><PERSON><PERSON><PERSON> (spm)", "generate": "Tạo", "createRouteFirst": "<PERSON><PERSON> lòng tạo tuyến đường trước bằng cách thêm điểm trên bản đồ", "alignToBikeRoutes": "<PERSON><PERSON><PERSON> Chỉnh <PERSON>", "alignToRunningPaths": "<PERSON><PERSON><PERSON> Chỉnh <PERSON>", "alignToWalkingPaths": "<PERSON><PERSON><PERSON> Chỉnh <PERSON>", "alignToRoad": "<PERSON><PERSON><PERSON> Chỉnh <PERSON>", "alignToBikeTooltip": "<PERSON><PERSON><PERSON> chỉnh tuyến đường theo làn xe đạp, đườ<PERSON> dành cho xe đạp và đường thân thiện với xe đạp sử dụng hồ sơ đạp xe Mapbox", "alignToRunningTooltip": "Căn chỉnh tuyến đường theo đường dành cho người đi bộ, vỉa hè và đường thân thiện với việc chạy bộ sử dụng hồ sơ đi bộ Mapbox", "alignToWalkingTooltip": "C<PERSON>n chỉnh tuyến đường theo đường đi bộ, đường mòn và đường thân thiện với người đi bộ sử dụng hồ sơ đi bộ Mapbox", "alignToRoadTooltip": "Căn chỉnh tuyến đường theo đường và lối đi sử dụng Mapbox"}, "export": {"title": "<PERSON><PERSON><PERSON>", "activitySummary": "<PERSON><PERSON><PERSON> T<PERSON>t Hoạt Động", "distance": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Thời Gian", "avgPace": "Tốc Độ TB", "avgHeartRate": "<PERSON><PERSON><PERSON><PERSON>", "noActivityData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu hoạt động", "generateActivity": "<PERSON><PERSON><PERSON>", "whatNext": "<PERSON><PERSON><PERSON><PERSON>?", "uploadToStrava": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>t hoạt động của bạn và tải lên <PERSON>", "link": "<PERSON><PERSON> Upload"}, "createAnother": {"title": "Tạo Ho<PERSON>t Độ<PERSON>", "description": "<PERSON><PERSON><PERSON> hoạt động mới sử dụng cùng tuyến đường hoặc tuyến đường khác", "button": "Tạo Ho<PERSON>t Độ<PERSON>"}, "designNew": {"title": "<PERSON><PERSON><PERSON><PERSON>ến Đ<PERSON><PERSON>", "description": "Quay lại trình tạo tuyến đường để thiết kế tuyến chạy mới", "button": "<PERSON><PERSON><PERSON><PERSON>ến Đ<PERSON><PERSON>"}, "exportOptions": "<PERSON><PERSON><PERSON>", "fileFormat": "Định <PERSON>ng File", "dataToInclude": "<PERSON><PERSON>", "heartRate": "<PERSON><PERSON><PERSON><PERSON>", "cadence": "<PERSON><PERSON><PERSON><PERSON>", "elevation": "<PERSON><PERSON>", "fileName": "<PERSON><PERSON><PERSON>", "downloadFile": "<PERSON><PERSON><PERSON>", "tokensRemaining": "Bạn còn {count} token{plural}", "authErrors": {"loginRequired": "Bạn phải đăng nhập để xuất hoạt động", "tokensRequired": "Bạn cần token để xuất hoạt động. <PERSON><PERSON> lòng mua thêm token.", "authError": "Lỗi xác thực. <PERSON><PERSON> lòng đăng nhập lại.", "activityNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy hoạt động trong cơ sở dữ liệu. <PERSON><PERSON> lòng làm mới trang.", "tokenRequired": "Bạn phải đăng nhập với token khả dụng để xuất hoạt động"}}}, "tokens": {"purchase": {"title": "<PERSON><PERSON>", "currentBalance": "<PERSON><PERSON> dư token hiện tại:", "tokens": "token", "selectPackage": "<PERSON><PERSON><PERSON> m<PERSON>t gó<PERSON>:", "payment": "<PERSON><PERSON>", "securePayment": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>o mật qua <PERSON>", "package": "<PERSON><PERSON><PERSON>", "payWithPayOS": "Thanh toán với PayOS", "creatingPaymentLink": "<PERSON><PERSON> tạo liên kết thanh toán...", "redirectMessage": "Bạn sẽ được chuyển hướng đến trang thanh toán bảo mật PayOS", "paymentSuccessful": "Thanh To<PERSON>ô<PERSON>!", "tokensAdded": "Token của bạn đã đư<PERSON>c thêm vào tài k<PERSON>n.", "purchaseMore": "<PERSON><PERSON>", "paymentCancelled": "<PERSON><PERSON>", "paymentCancelledMessage": "<PERSON><PERSON> toán của bạn đã bị hủy. <PERSON>h<PERSON>ng có khoản phí nào đư<PERSON><PERSON> t<PERSON>h.", "tryAgain": "<PERSON><PERSON><PERSON>", "selectPackageMessage": "<PERSON><PERSON> lòng chọn một gói để tiếp tục", "paymentCompleted": "Thanh toán hoàn tất thành công! Token của bạn đã đư<PERSON>c thêm vào tài k<PERSON>n.", "paymentCancelledStatus": "<PERSON><PERSON> toán đã bị hủy. <PERSON><PERSON><PERSON><PERSON> có khoản phí nào đ<PERSON><PERSON><PERSON> t<PERSON>.", "failedToLoad": "<PERSON><PERSON><PERSON><PERSON> thể tải gói. <PERSON><PERSON> lòng làm mới trang.", "paymentLinkError": "<PERSON><PERSON><PERSON><PERSON> thể tạo liên kết thanh toán. <PERSON><PERSON> lòng thử lại."}}, "profile": {"title": "Hồ Sơ", "personalInfo": "Thông Tin Cá Nhân", "accountStats": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "memberSince": "<PERSON><PERSON><PERSON><PERSON>", "activitiesCreated": "Hoạt Động Đ<PERSON> T<PERSON>", "availableTokens": "Token K<PERSON>ả <PERSON>", "tokensDescription": "To<PERSON> được sử dụng để tạo và xuất hoạt động", "purchaseMoreTokens": "<PERSON><PERSON>", "loginRequired": "<PERSON><PERSON> lòng đăng nhập để xem hồ sơ của bạn.", "loginButton": "<PERSON><PERSON><PERSON>"}, "common": {"loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Đ<PERSON> xảy ra lỗi", "success": "Th<PERSON>nh công!", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "close": "Đ<PERSON><PERSON>", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "refresh": "<PERSON><PERSON><PERSON>"}, "language": {"switch": "<PERSON><PERSON><PERSON><PERSON>", "english": "English", "vietnamese": "Tiếng <PERSON>"}}