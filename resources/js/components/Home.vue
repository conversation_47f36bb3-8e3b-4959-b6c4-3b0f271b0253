<template>
    <div>
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-strava-orange mb-4">{{ $t('home.title') }}</h1>
            <p class="text-xl text-strava-grayMedium">{{ $t('home.subtitle') }}</p>

            <!-- New All-in-One Feature Highlight -->
            <div class="mt-8 bg-strava-surface p-6 rounded-lg inline-block shadow-md border border-gray-200">
                <h2 class="text-2xl font-bold text-strava-gray mb-2">{{ $t('home.newFeature.title') }}</h2>
                <p class="mb-4 text-strava-grayLight">{{ $t('home.newFeature.description') }}</p>
                <router-link :to="{ name: 'generate-activity' }" class="inline-block bg-strava-orange text-white py-3 px-6 rounded-lg hover:bg-strava-orangeLight font-bold text-lg">{{ $t('home.newFeature.button') }}</router-link>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-bold text-strava-gray mb-4">{{ $t('home.features.createRoutes.title') }}</h2>
                <p class="mb-4 text-strava-grayLight">{{ $t('home.features.createRoutes.description') }}</p>
                <router-link :to="{ name: 'generate-activity' }" class="inline-block bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">{{ $t('home.features.createRoutes.button') }}</router-link>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-bold text-strava-gray mb-4">{{ $t('home.features.generateActivities.title') }}</h2>
                <p class="mb-4 text-strava-grayLight">{{ $t('home.features.generateActivities.description') }}</p>
                <router-link :to="{ name: 'generate-activity' }" class="inline-block bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">{{ $t('home.features.generateActivities.button') }}</router-link>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-bold text-strava-gray mb-4">{{ $t('home.features.exportActivities.title') }}</h2>
                <p class="mb-4 text-strava-grayLight">{{ $t('home.features.exportActivities.description') }}</p>
                <router-link :to="{ name: 'export-activity' }" class="inline-block bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">{{ $t('home.features.exportActivities.button') }}</router-link>
            </div>
        </div>

        <div class="bg-white p-8 rounded-lg shadow-md border border-gray-100">
            <h2 class="text-2xl font-bold text-strava-gray mb-4">{{ $t('home.whyUse.title') }}</h2>
            <ul class="list-disc pl-6 space-y-2 text-strava-grayMedium">
                <li>{{ $t('home.whyUse.reason1') }}</li>
                <li>{{ $t('home.whyUse.reason2') }}</li>
                <li>{{ $t('home.whyUse.reason3') }}</li>
                <li>{{ $t('home.whyUse.reason4') }}</li>
                <li>{{ $t('home.whyUse.reason5') }}</li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Home'
}
</script>
